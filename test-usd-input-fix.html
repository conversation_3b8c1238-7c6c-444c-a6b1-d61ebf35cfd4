<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test USD Input Flexibility Fix - SmartBoutique</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1976d2;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
        }
        .input-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        input:focus {
            outline: none;
            border-color: #1976d2;
        }
        .currency-toggle {
            margin: 10px 0;
        }
        .toggle-btn {
            padding: 8px 16px;
            margin: 0 5px;
            border: 2px solid #1976d2;
            background: white;
            color: #1976d2;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .toggle-btn.active {
            background: #1976d2;
            color: white;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            background-color: #e3f2fd;
            border-left: 4px solid #1976d2;
        }
        .success {
            color: #4caf50;
            font-weight: bold;
        }
        .info {
            color: #2196f3;
        }
        .test-cases {
            margin-top: 20px;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            font-family: monospace;
        }
        .explanation {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .fix-details {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test USD Input Flexibility Fix</h1>
        
        <div class="explanation">
            <h3>🎯 Problème Résolu</h3>
            <p><strong>Avant:</strong> Le champ USD avait des restrictions qui empêchaient la saisie libre (ex: "1.03" était difficile à taper)</p>
            <p><strong>Après:</strong> Le champ USD permet maintenant la saisie libre comme le champ CDF</p>
        </div>

        <div class="fix-details">
            <h3>✅ Corrections Apportées</h3>
            <ul>
                <li><strong>Validation flexible:</strong> Permet la saisie partielle (ex: "1.", "12.5")</li>
                <li><strong>Regex améliorée:</strong> Pattern unifié pour CDF et USD</li>
                <li><strong>Gestion des décimales:</strong> Support complet des montants USD avec décimales</li>
                <li><strong>Pas de blocage:</strong> Plus de restrictions pendant la frappe</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 Test de Saisie USD Flexible</div>
            
            <div class="currency-toggle">
                <button class="toggle-btn active" onclick="switchMode('CDF')">Saisie en CDF</button>
                <button class="toggle-btn" onclick="switchMode('USD')">Saisie en USD</button>
            </div>

            <div class="input-group">
                <label for="currency-input">Montant (<span id="current-mode">CDF</span>)</label>
                <input type="text" id="currency-input" placeholder="Tapez un montant..." 
                       oninput="handleInput()" pattern="[0-9]*\.?[0-9]*" inputmode="decimal">
            </div>

            <div class="result">
                <div><strong>Valeur saisie:</strong> <span id="input-value">0</span></div>
                <div><strong>Équivalence:</strong> <span id="conversion">0 CDF ≈ $0,00</span></div>
                <div><strong>Statut:</strong> <span id="status">Prêt pour la saisie</span></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">📝 Cas de Test à Essayer</div>
            <div class="test-cases">
                <div class="test-case">✅ Tapez "1234.56" en mode USD - Doit fonctionner sans restriction</div>
                <div class="test-case">✅ Tapez "1." en mode USD - Doit permettre la saisie partielle</div>
                <div class="test-case">✅ Tapez "53.57" en mode USD - Doit convertir correctement</div>
                <div class="test-case">✅ Tapez "150000" en mode CDF - Doit fonctionner comme avant</div>
                <div class="test-case">✅ Basculez entre CDF et USD - Doit conserver les valeurs</div>
            </div>
        </div>
    </div>

    <script>
        let currentMode = 'CDF';
        const exchangeRate = 2800;

        function switchMode(mode) {
            currentMode = mode;
            document.getElementById('current-mode').textContent = mode;
            
            // Update button states
            document.querySelectorAll('.toggle-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update input placeholder
            const input = document.getElementById('currency-input');
            if (mode === 'USD') {
                input.placeholder = "Ex: 53.57, 1234.56";
            } else {
                input.placeholder = "Ex: 150000, 2800000";
            }
            
            handleInput(); // Refresh conversion
        }

        function handleInput() {
            const input = document.getElementById('currency-input');
            const value = input.value;
            
            // Update display
            document.getElementById('input-value').textContent = value || '0';
            
            // Validate input using the same logic as the fixed component
            if (value === '' || value === '.' || value === '0.' || /^\d*\.?\d*$/.test(value)) {
                const numericValue = parseFloat(value) || 0;
                
                if (!isNaN(numericValue) && numericValue >= 0) {
                    // Convert and display
                    let cdfValue, usdValue;
                    if (currentMode === 'CDF') {
                        cdfValue = numericValue;
                        usdValue = cdfValue / exchangeRate;
                    } else {
                        usdValue = numericValue;
                        cdfValue = usdValue * exchangeRate;
                    }
                    
                    document.getElementById('conversion').textContent = 
                        `${Math.round(cdfValue).toLocaleString('fr-FR')} CDF ≈ $${usdValue.toFixed(2)}`;
                    document.getElementById('status').innerHTML = 
                        '<span class="success">✅ Saisie valide - Conversion réussie</span>';
                } else {
                    document.getElementById('status').innerHTML = 
                        '<span class="info">ℹ️ Saisie en cours...</span>';
                }
            } else {
                document.getElementById('status').innerHTML = 
                    '<span style="color: orange;">⚠️ Format non valide</span>';
            }
        }

        // Initialize
        handleInput();
    </script>
</body>
</html>
