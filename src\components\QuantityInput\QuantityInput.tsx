import React, { useState, useEffect } from 'react';
import {
  Box,
  IconButton,
  TextField,
  InputAdornment,
} from '@mui/material';
import { Add, Remove } from '@mui/icons-material';

interface QuantityInputProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  disabled?: boolean;
  size?: 'small' | 'medium';
  showButtons?: boolean;
  allowDirectInput?: boolean;
  label?: string;
  error?: boolean;
  helperText?: string;
}

export const QuantityInput: React.FC<QuantityInputProps> = ({
  value,
  onChange,
  min = 1,
  max = Number.MAX_SAFE_INTEGER, // Removed artificial limits - supports large-scale inventory
  disabled = false,
  size = 'small',
  showButtons = true,
  allowDirectInput = true,
  label,
  error = false,
  helperText,
}) => {
  const [textValue, setTextValue] = useState<string>(value.toString());

  // Update local state when prop value changes
  useEffect(() => {
    setTextValue(value.toString());
  }, [value]);

  // Handle direct text input changes - Enhanced for optimal direct input UX
  const handleTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = event.target.value;
    setTextValue(inputValue);

    // Allow empty input for better typing experience
    if (inputValue === '') {
      return;
    }

    // Parse and validate the input - Support decimal input for better UX
    const numericValue = parseFloat(inputValue);

    // Only update if it's a valid number
    if (!isNaN(numericValue)) {
      // Round to nearest integer for quantity inputs
      const roundedValue = Math.round(numericValue);
      const clampedValue = Math.max(min, Math.min(max, roundedValue));

      // Call onChange immediately for better responsiveness
      if (roundedValue >= min && roundedValue <= max) {
        onChange(roundedValue);
      } else if (clampedValue !== value) {
        onChange(clampedValue);
      }
    }
  };

  // Handle text input blur (ensure valid value) - Enhanced for optimal direct input UX
  const handleTextBlur = () => {
    // If empty or invalid, use current value or minimum
    if (textValue === '' || isNaN(parseFloat(textValue))) {
      const fallbackValue = value || min;
      setTextValue(fallbackValue.toString());
      if (fallbackValue !== value) {
        onChange(fallbackValue);
      }
      return;
    }

    // Support decimal input but round to integer for quantities
    const numericValue = Math.round(parseFloat(textValue));
    const clampedValue = Math.max(min, Math.min(max, numericValue));

    // Update display and value if needed
    setTextValue(clampedValue.toString());
    if (clampedValue !== value) {
      onChange(clampedValue);
    }
  };

  // Handle increment button
  const handleIncrement = () => {
    const newValue = Math.min(max, value + 1);
    onChange(newValue);
  };

  // Handle decrement button
  const handleDecrement = () => {
    const newValue = Math.max(min, value - 1);
    onChange(newValue);
  };

  // Handle key press for direct input - Enhanced for optimal direct input UX
  const handleKeyPress = (event: React.KeyboardEvent) => {
    // Allow numbers, navigation keys, and common editing keys
    const allowedKeys = [
      'Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
      'Tab', 'Enter', 'Home', 'End', 'Escape', '.'
    ];

    // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X for copy/paste operations
    if (event.ctrlKey && ['a', 'c', 'v', 'x'].includes(event.key.toLowerCase())) {
      return;
    }

    // Allow numbers, decimal point, and allowed keys for better input flexibility
    if (!/[0-9.]/.test(event.key) && !allowedKeys.includes(event.key)) {
      event.preventDefault();
    }

    // Prevent multiple decimal points
    if (event.key === '.' && textValue.includes('.')) {
      event.preventDefault();
    }
  };

  if (!allowDirectInput && showButtons) {
    // Button-only mode (original behavior)
    return (
      <Box display="flex" alignItems="center" gap={0.5}>
        <IconButton
          size={size}
          onClick={handleDecrement}
          disabled={disabled || value <= min}
        >
          <Remove fontSize={size} />
        </IconButton>
        <Box 
          sx={{ 
            minWidth: size === 'small' ? '30px' : '40px', 
            textAlign: 'center',
            fontWeight: 'medium',
            fontSize: size === 'small' ? '0.875rem' : '1rem'
          }}
        >
          {value}
        </Box>
        <IconButton
          size={size}
          onClick={handleIncrement}
          disabled={disabled || value >= max}
        >
          <Add fontSize={size} />
        </IconButton>
      </Box>
    );
  }

  if (allowDirectInput && !showButtons) {
    // Text input only mode - using type="text" to remove spinners
    return (
      <TextField
        size={size}
        label={label}
        type="text"
        value={textValue}
        onChange={handleTextChange}
        onBlur={handleTextBlur}
        onKeyPress={handleKeyPress}
        disabled={disabled}
        error={error}
        helperText={helperText}
        inputProps={{
          // Using text input with numeric validation to avoid spinners
          inputMode: 'numeric', // Mobile keyboards will show numeric keypad
          pattern: '[0-9]*', // Additional hint for mobile browsers
        }}
        sx={{
          minWidth: '80px',
          // Hide any potential spinners across all browsers
          '& input[type=number]': {
            MozAppearance: 'textfield',
            '&::-webkit-outer-spin-button': {
              WebkitAppearance: 'none',
              margin: 0,
            },
            '&::-webkit-inner-spin-button': {
              WebkitAppearance: 'none',
              margin: 0,
            },
          },
        }}
      />
    );
  }

  // Combined mode (buttons + text input) - Enhanced for direct input priority
  return (
    <TextField
      size={size}
      label={label}
      type="text"
      value={textValue}
      onChange={handleTextChange}
      onBlur={handleTextBlur}
      onKeyPress={handleKeyPress}
      disabled={disabled}
      error={error}
      helperText={helperText || (showButtons ? "Tapez directement la quantité (recommandé) ou utilisez +/-" : "Tapez directement la quantité désirée")}
      placeholder="Tapez la quantité..."
      inputProps={{
        // Using text input with numeric validation to avoid spinners
        inputMode: 'numeric', // Mobile keyboards will show numeric keypad
        pattern: '[0-9]*', // Additional hint for mobile browsers
        style: {
          textAlign: 'center',
          fontSize: size === 'small' ? '0.875rem' : '1rem',
          fontWeight: 500
        }
      }}
      InputProps={{
        startAdornment: showButtons ? (
          <InputAdornment position="start">
            <IconButton
              size={size}
              onClick={handleDecrement}
              disabled={disabled || value <= min}
              edge="start"
              sx={{
                opacity: 0.6,
                '&:hover': { opacity: 0.8 },
                transition: 'opacity 0.2s ease'
              }}
            >
              <Remove fontSize={size} />
            </IconButton>
          </InputAdornment>
        ) : undefined,
        endAdornment: showButtons ? (
          <InputAdornment position="end">
            <IconButton
              size={size}
              onClick={handleIncrement}
              disabled={disabled || value >= max}
              edge="end"
              sx={{
                opacity: 0.6,
                '&:hover': { opacity: 0.8 },
                transition: 'opacity 0.2s ease'
              }}
            >
              <Add fontSize={size} />
            </IconButton>
          </InputAdornment>
        ) : undefined,
      }}
      sx={{
        minWidth: showButtons ? '160px' : '100px',
        '& .MuiOutlinedInput-root': {
          '&:hover fieldset': {
            borderColor: 'primary.main',
          },
          '&.Mui-focused fieldset': {
            borderWidth: 2,
            borderColor: 'primary.main',
          },
          '& input': {
            cursor: 'text',
            '&:focus': {
              backgroundColor: 'rgba(25, 118, 210, 0.04)',
            }
          }
        },
        // Hide any potential spinners across all browsers
        '& input[type=number]': {
          MozAppearance: 'textfield',
          '&::-webkit-outer-spin-button': {
            WebkitAppearance: 'none',
            margin: 0,
          },
          '&::-webkit-inner-spin-button': {
            WebkitAppearance: 'none',
            margin: 0,
          },
        },
      }}
    />
  );
};
