# 🚀 SmartBoutique Quick Reference Card

## 📋 Essential Commands

### 🌐 Web Development (Vite)
```bash
# Start development server
npm run dev:vite

# Build for production
npm run build:vite

# Preview production build
npm run preview

# Access: http://localhost:5173
```

### 💻 Desktop Development (Electron)
```bash
# Start desktop app (with hot reload)
npm run dev

# Build desktop app
npm run build

# Create Windows distribution
npm run dist:win

# Create portable executable
npm run dist:portable
```

### 📱 Mobile Development (Android)
```bash
# Add Android platform (one-time)
npm run mobile:add:android

# Build and sync to Android
npm run mobile:build

# Run on Android device
npm run mobile:dev

# Open Android Studio
npm run mobile:open:android
```

## 🔧 Quick Setup Checklist

### Prerequisites
- [ ] Node.js v18+ installed
- [ ] npm v8+ available
- [ ] Git installed
- [ ] For Android: Android Studio + JDK 11/17

### Initial Setup
```bash
# 1. Install dependencies
npm install

# 2. Test web version
npm run dev:vite

# 3. Test desktop version
npm run dev

# 4. Setup mobile (if needed)
npm run mobile:add:android
npm run mobile:build
```

## 🐛 Common Issues & Quick Fixes

### Port Already in Use
```bash
npx kill-port 5173
# or
npm run dev:vite -- --port 3000
```

### Electron Won't Start
```bash
npm run postinstall
npm run build:electron
```

### Android Build Fails
```bash
cd android
./gradlew clean
cd ..
npm run mobile:build
```

### SQLite Issues
```bash
npm uninstall better-sqlite3
npm install better-sqlite3
npm run postinstall
```

## 📁 Key File Locations

```
SmartBoutique/
├── src/                     # Main app code
├── electron/main.ts         # Desktop main process
├── android/                 # Mobile Android project
├── dist/                    # Built web app
├── dist-electron/           # Built desktop app
├── release-final/           # Distribution packages
├── vite.config.ts          # Web dev configuration
├── capacitor.config.ts     # Mobile configuration
└── package.json            # Scripts and dependencies
```

## 🎯 Platform-Specific URLs & Locations

### Web Development
- **Dev Server**: `http://localhost:5173`
- **Built Files**: `dist/`
- **Config**: `vite.config.ts`

### Desktop Development
- **Dev Mode**: Electron window opens automatically
- **Built Files**: `dist-electron/`
- **Distribution**: `release-final/`
- **Config**: `electron/main.ts`

### Mobile Development
- **Android Project**: `android/`
- **APK Location**: `android/app/build/outputs/apk/`
- **Config**: `capacitor.config.ts`

## 🔍 Debugging Quick Access

### Web (Browser DevTools)
- **Open**: F12 or right-click → Inspect
- **Console**: Check for JavaScript errors
- **Network**: Monitor API calls
- **Application**: Check localStorage/SQLite

### Desktop (Electron DevTools)
- **Auto-opens** in development mode
- **Manual**: `Ctrl+Shift+I` in Electron window
- **Console**: Same as browser DevTools

### Mobile (Chrome DevTools)
- **Enable**: USB Debugging on Android device
- **Access**: `chrome://inspect` in Chrome browser
- **Select**: Your device and SmartBoutique app

## ⚡ Performance Tips

### Development
- Use **web version** for fastest iteration
- **Hot reload** works best with Vite
- **Desktop version** for testing native features

### Production
- **Build optimized** versions: `npm run build`
- **Test on target platform** before distribution
- **Use portable exe** for easy client distribution

## 📦 Distribution Checklist

### Web Deployment
- [ ] Run `npm run build:vite`
- [ ] Test `dist/` folder contents
- [ ] Upload to web server

### Desktop Distribution
- [ ] Run `npm run dist:win`
- [ ] Test `SmartBoutique-Portable-1.1.0.exe`
- [ ] Verify on clean Windows machine

### Mobile Distribution
- [ ] Build release APK in Android Studio
- [ ] Test on multiple Android devices
- [ ] Consider Google Play Store requirements

## 🎨 UI Adaptation

SmartBoutique automatically adapts to each platform:

- **Web**: Responsive design, works on any screen size
- **Desktop**: Optimized for mouse and keyboard
- **Mobile**: Touch-friendly, larger buttons, simplified navigation

## 💾 Data Storage

- **Web**: SQLite.js (in-memory) + localStorage
- **Desktop**: better-sqlite3 (native SQLite)
- **Mobile**: Capacitor Preferences + SQLite

## 🌍 Multi-Language Support

- **Primary**: French (Français)
- **Currency**: CDF (primary) + USD (secondary)
- **Exchange Rate**: 2800 CDF = 1 USD

## 🔐 Security Notes

- **Web**: Browser security restrictions apply
- **Desktop**: Full system access available
- **Mobile**: Android permissions required for file access

## 📞 Support Resources

- **Main Tutorial**: `SMARTBOUTIQUE_DEVELOPER_TUTORIAL.md`
- **Project Structure**: Check `src/` folder organization
- **Configuration**: Review `package.json` scripts
- **Platform Detection**: Use `src/utils/platform.ts`

---

## 🎯 Quick Start (30 seconds)

```bash
# Clone project and start web development
git clone [repository-url]
cd SmartBoutique
npm install
npm run dev:vite
# Open http://localhost:5173
```

**That's it!** SmartBoutique is now running in your browser with demo data loaded.

---

*Quick Reference for SmartBoutique v1.1.0 - January 2025*
