import React, { useEffect, useState } from 'react';
import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box } from '@mui/material';
import { frFR } from '@mui/material/locale';

// Services
import { adaptiveAuthService } from '@/services/adaptive-auth';
import { adaptiveStorageService } from '@/services/adaptive-storage';

// Utils
import { getPlatformInfo, getPlatformTheme } from '@/utils/platform';

// Hooks
import { useFocusFix } from '@/hooks/useFocusFix';

// Components
import Layout from '@/components/Layout/Layout';
import ProtectedRoute from '@/components/ProtectedRoute';
import LoginPage from '@/pages/Login/LoginPage';
import DashboardPage from '@/pages/Dashboard/DashboardPage';
import ProductsPage from '@/pages/Products/ProductsPage';
import SalesPage from '@/pages/Sales/SalesPage';
import DebtsPage from '@/pages/Debts/DebtsPage';
import ExpensesPage from '@/pages/Expenses/ExpensesPage';
import ReportsPage from '@/pages/Reports/ReportsPage';
import UsersPage from '@/pages/Users/<USER>';
import SettingsPage from '@/pages/Settings/SettingsPage';
import EmployeePaymentsPage from '@/pages/EmployeePayments/EmployeePaymentsPage';

// Types
import { User } from '@/types';

// Create theme with French locale and platform-specific adaptations
const createAppTheme = () => {
  const platformTheme = getPlatformTheme();

  return createTheme(
    {
      palette: {
        primary: {
          main: '#1976d2',
        },
        secondary: {
          main: '#dc004e',
        },
        background: {
          default: '#f5f5f5',
        },
      },
      typography: {
        fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
        h4: {
          fontWeight: 600,
        },
        h5: {
          fontWeight: 600,
        },
        h6: {
          fontWeight: 600,
        },
      },
      components: {
        MuiButton: {
          styleOverrides: {
            root: {
              textTransform: 'none',
              ...platformTheme.components?.MuiButton?.styleOverrides?.root,
            },
          },
        },
        MuiCard: {
          styleOverrides: {
            root: {
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            },
          },
        },
        MuiIconButton: {
          styleOverrides: {
            root: {
              ...platformTheme.components?.MuiIconButton?.styleOverrides?.root,
            },
          },
        },
        MuiTableCell: {
          styleOverrides: {
            root: {
              ...platformTheme.components?.MuiTableCell?.styleOverrides?.root,
            },
          },
        },
        // Global styles to hide number input spinners across all browsers
        MuiCssBaseline: {
          styleOverrides: {
            // Hide spinners for all number inputs globally
            'input[type=number]': {
              MozAppearance: 'textfield', // Firefox
              '&::-webkit-outer-spin-button': {
                WebkitAppearance: 'none', // Chrome, Safari, Edge
                margin: 0,
              },
              '&::-webkit-inner-spin-button': {
                WebkitAppearance: 'none', // Chrome, Safari, Edge
                margin: 0,
              },
            },
            // Additional coverage for any remaining number inputs
            'input[type="number"]': {
              MozAppearance: 'textfield',
              '&::-webkit-outer-spin-button': {
                WebkitAppearance: 'none',
                margin: 0,
              },
              '&::-webkit-inner-spin-button': {
                WebkitAppearance: 'none',
                margin: 0,
              },
            },
          },
        },
      },
    },
    frFR
  );
};



const App: React.FC = () => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [theme, setTheme] = useState(() => createAppTheme());

  // Apply focus fix for input fields
  useFocusFix();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        if (process.env.NODE_ENV === 'development') {
          console.log('SmartBoutique: Initializing app...');
          const platformInfo = getPlatformInfo();
          console.log('Platform info:', platformInfo);

          // Check localStorage availability
          try {
            const testKey = '__localStorage_test__';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            console.log('SmartBoutique: localStorage is available');
          } catch (e) {
            console.error('SmartBoutique: localStorage is not available:', e);
            throw new Error('localStorage is not available. This may be due to Electron security settings.');
          }
        }

        // Check if data has been reset and should remain clean
        const dataReset = localStorage.getItem('smartboutique_data_reset');
        if (dataReset === 'true') {
          // Data was reset, only initialize essential users if they don't exist
          const users = await adaptiveStorageService.getUsers();
          if (users.length === 0) {
            // Initialize only essential users without demo data
            const defaultUsers = [
              {
                id: '1',
                nom: 'Super Admin',
                email: '<EMAIL>',
                role: 'super_admin',
                motDePasse: 'admin123',
                dateCreation: new Date().toISOString(),
                actif: true
              },
              {
                id: '2',
                nom: 'Gestionnaire',
                email: '<EMAIL>',
                role: 'admin',
                motDePasse: 'manager123',
                dateCreation: new Date().toISOString(),
                actif: true
              },
              {
                id: '3',
                nom: 'Employé',
                email: '<EMAIL>',
                role: 'employee',
                motDePasse: 'employee123',
                dateCreation: new Date().toISOString(),
                actif: true
              }
            ];
            await adaptiveStorageService.setUsers(defaultUsers);
          }
          if (process.env.NODE_ENV === 'development') {
            console.log('SmartBoutique: Clean state maintained after data reset');
          }
        } else {
          // Normal initialization with demo data
          await adaptiveStorageService.initializeDefaultData();
          if (process.env.NODE_ENV === 'development') {
            console.log('SmartBoutique: Default data initialized');
          }
        }

        // Try to migrate data from desktop to mobile if needed
        const platformInfo = getPlatformInfo();
        if (platformInfo.isMobile) {
          await adaptiveStorageService.migrateFromDesktop();
          if (process.env.NODE_ENV === 'development') {
            console.log('SmartBoutique: Migration check completed');
          }
        }

        // Initialize auth service and check if user is logged in
        await adaptiveAuthService.initialize();
        const user = adaptiveAuthService.getCurrentUser();
        setCurrentUser(user);
        if (process.env.NODE_ENV === 'development') {
          console.log('SmartBoutique: User state set:', user ? 'logged in' : 'not logged in');
        }

        setIsLoading(false);
        if (process.env.NODE_ENV === 'development') {
          console.log('SmartBoutique: App initialization complete');
        }
      } catch (err) {
        console.error('SmartBoutique: Error during initialization:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  const handleLogin = (user: User) => {
    setCurrentUser(user);
  };

  const handleLogout = async () => {
    await adaptiveAuthService.logout();
    setCurrentUser(null);
  };

  if (error) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Box
          display="flex"
          flexDirection="column"
          justifyContent="center"
          alignItems="center"
          minHeight="100vh"
          p={3}
        >
          <h2>Erreur de chargement</h2>
          <p>Une erreur s'est produite lors du chargement de l'application:</p>
          <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
            {error}
          </pre>
          <button onClick={() => window.location.reload()}>
            Recharger l'application
          </button>
        </Box>
      </ThemeProvider>
    );
  }

  if (isLoading) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="100vh"
        >
          Chargement...
        </Box>
      </ThemeProvider>
    );
  }

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Routes>
          {/* Login Route */}
          <Route
            path="/login"
            element={
              currentUser ? (
                <Navigate to="/dashboard" replace />
              ) : (
                <LoginPage onLogin={handleLogin} />
              )
            }
          />

          {/* Protected Routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <Layout currentUser={currentUser} onLogout={handleLogout} />
              </ProtectedRoute>
            }
          >
            {/* Dashboard */}
            <Route
              index
              element={<Navigate to="/dashboard" replace />}
            />
            <Route
              path="dashboard"
              element={
                <ProtectedRoute requiredPermission="canViewDashboard">
                  <DashboardPage />
                </ProtectedRoute>
              }
            />

            {/* Products */}
            <Route
              path="products"
              element={
                <ProtectedRoute requiredPermission="canViewProducts">
                  <ProductsPage />
                </ProtectedRoute>
              }
            />

            {/* Sales */}
            <Route
              path="sales"
              element={
                <ProtectedRoute requiredPermission="canViewSales">
                  <SalesPage />
                </ProtectedRoute>
              }
            />

            {/* Debts */}
            <Route
              path="debts"
              element={
                <ProtectedRoute requiredPermission="canViewDebts">
                  <DebtsPage />
                </ProtectedRoute>
              }
            />

            {/* Expenses */}
            <Route
              path="expenses"
              element={
                <ProtectedRoute requiredPermission="canViewExpenses">
                  <ExpensesPage />
                </ProtectedRoute>
              }
            />

            {/* Employee Payments */}
            <Route
              path="employee-payments"
              element={
                <ProtectedRoute requiredPermission="canViewEmployeePayments">
                  <EmployeePaymentsPage />
                </ProtectedRoute>
              }
            />

            {/* Reports */}
            <Route
              path="reports"
              element={
                <ProtectedRoute requiredPermission="canViewReports">
                  <ReportsPage />
                </ProtectedRoute>
              }
            />

            {/* Users */}
            <Route
              path="users"
              element={
                <ProtectedRoute requiredPermission="canViewUsers">
                  <UsersPage />
                </ProtectedRoute>
              }
            />

            {/* Settings */}
            <Route
              path="settings"
              element={
                <ProtectedRoute requiredPermission="canViewSettings">
                  <SettingsPage />
                </ProtectedRoute>
              }
            />
          </Route>

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Router>
    </ThemeProvider>
  );
};

export default App;
