import{W as t}from"./index-DCJ_0XsD.js";import"./mui-oD-jvHl6.js";import"./vendor-B_Ch-B_d.js";import"./utils-Ch7HAeVX.js";import"./charts-UhR5A4U7.js";class e extends t{constructor(){super(...arguments),this.group="CapacitorStorage"}async configure({group:t}){"string"==typeof t&&(this.group=t)}async get(t){return{value:this.impl.getItem(this.applyPrefix(t.key))}}async set(t){this.impl.setItem(this.applyPrefix(t.key),t.value)}async remove(t){this.impl.removeItem(this.applyPrefix(t.key))}async keys(){return{keys:this.rawKeys().map(t=>t.substring(this.prefix.length))}}async clear(){for(const t of this.rawKeys())this.impl.removeItem(t)}async migrate(){var t;const e=[],i=[],s="_cap_",r=Object.keys(this.impl).filter(t=>0===t.indexOf(s));for(const o of r){const s=o.substring(5),r=null!==(t=this.impl.getItem(o))&&void 0!==t?t:"",{value:p}=await this.get({key:s});"string"==typeof p?i.push(s):(await this.set({key:s,value:r}),e.push(s))}return{migrated:e,existing:i}}async removeOld(){const t=Object.keys(this.impl).filter(t=>0===t.indexOf("_cap_"));for(const e of t)this.impl.removeItem(e)}get impl(){return window.localStorage}get prefix(){return"NativeStorage"===this.group?"":`${this.group}.`}rawKeys(){return Object.keys(this.impl).filter(t=>0===t.indexOf(this.prefix))}applyPrefix(t){return this.prefix+t}}export{e as PreferencesWeb};
